INFO:     Will watch for changes in these directories: ['/Users/<USER>/Documents/augment-projects/PrintMind/backend']
INFO:     Uvicorn running on http://0.0.0.0:8000 (Press CTRL+C to quit)
INFO:     Started reloader process [17657] using WatchFiles
Process SpawnProcess-1:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/pdf.py", line 11, in <module>
    from app.services.pdf_service import PDFService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/pdf_service.py", line 10, in <module>
    from weasyprint import HTML, CSS
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/__init__.py", line 387, in <module>
    from .css import preprocess_stylesheet  # noqa isort:skip
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/css/__init__.py", line 25, in <module>
    from . import computed_values, counters, media_queries
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/css/computed_values.py", line 11, in <module>
    from ..text.ffi import ffi, pango, units_to_double
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/text/ffi.py", line 428, in <module>
    gobject = _dlopen(
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/text/ffi.py", line 417, in _dlopen
    return ffi.dlopen(names[0])  # pragma: no cover
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 150, in dlopen
    lib, function_cache = _make_ffi_library(self, name, flags)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 834, in _make_ffi_library
    backendlib = _load_backend_lib(backend, libname, flags)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 829, in _load_backend_lib
    raise OSError(msg)
OSError: cannot load library 'gobject-2.0-0': dlopen(gobject-2.0-0, 0x0002): tried: 'gobject-2.0-0' (no such file), '/System/Volumes/Preboot/Cryptexes/OSgobject-2.0-0' (no such file), '/usr/lib/gobject-2.0-0' (no such file, not in dyld cache), 'gobject-2.0-0' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0'

-----

WeasyPrint could not import some external libraries. Please carefully follow the installation steps before reporting an issue:
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#troubleshooting 

-----

WARNING:  WatchFiles detected changes in 'app/services/pdf_service_simple.py'. Reloading...
Process SpawnProcess-2:
Traceback (most recent call last):
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 315, in _bootstrap
    self.run()
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/multiprocessing/process.py", line 108, in run
    self._target(*self._args, **self._kwargs)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/_subprocess.py", line 76, in subprocess_started
    target(sockets=sockets)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 61, in run
    return asyncio.run(self.serve(sockets=sockets))
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/asyncio/runners.py", line 44, in run
    return loop.run_until_complete(main)
  File "uvloop/loop.pyx", line 1518, in uvloop.loop.Loop.run_until_complete
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/server.py", line 68, in serve
    config.load()
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/config.py", line 467, in load
    self.loaded_app = import_from_string(self.app)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/uvicorn/importer.py", line 21, in import_from_string
    module = importlib.import_module(module_str)
  File "/Library/Developer/CommandLineTools/Library/Frameworks/Python3.framework/Versions/3.9/lib/python3.9/importlib/__init__.py", line 127, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1030, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1007, in _find_and_load
  File "<frozen importlib._bootstrap>", line 986, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 680, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 850, in exec_module
  File "<frozen importlib._bootstrap>", line 228, in _call_with_frames_removed
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/main.py", line 11, in <module>
    from app.api import documents, layout, pdf, fonts
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/api/pdf.py", line 11, in <module>
    from app.services.pdf_service import PDFService
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/app/services/pdf_service.py", line 10, in <module>
    from weasyprint import HTML, CSS
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/__init__.py", line 387, in <module>
    from .css import preprocess_stylesheet  # noqa isort:skip
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/css/__init__.py", line 25, in <module>
    from . import computed_values, counters, media_queries
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/css/computed_values.py", line 11, in <module>
    from ..text.ffi import ffi, pango, units_to_double
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/text/ffi.py", line 428, in <module>
    gobject = _dlopen(
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/weasyprint/text/ffi.py", line 417, in _dlopen
    return ffi.dlopen(names[0])  # pragma: no cover
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 150, in dlopen
    lib, function_cache = _make_ffi_library(self, name, flags)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 834, in _make_ffi_library
    backendlib = _load_backend_lib(backend, libname, flags)
  File "/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/cffi/api.py", line 829, in _load_backend_lib
    raise OSError(msg)
OSError: cannot load library 'gobject-2.0-0': dlopen(gobject-2.0-0, 0x0002): tried: 'gobject-2.0-0' (no such file), '/System/Volumes/Preboot/Cryptexes/OSgobject-2.0-0' (no such file), '/usr/lib/gobject-2.0-0' (no such file, not in dyld cache), 'gobject-2.0-0' (no such file).  Additionally, ctypes.util.find_library() did not manage to locate a library called 'gobject-2.0-0'

-----

WeasyPrint could not import some external libraries. Please carefully follow the installation steps before reporting an issue:
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#installation
https://doc.courtbouillon.org/weasyprint/stable/first_steps.html#troubleshooting 

-----

WARNING:  WatchFiles detected changes in 'app/services/pdf_service.py', 'app/services/pdf_service_original.py', 'app/services/pdf_service_simple.py'. Reloading...
INFO:     Started server process [18476]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
