"""
文档处理服务
负责文档格式转换、内容提取等功能
"""

import os
import aiofiles
from docx import Document
from docx.document import Document as DocxDocument
from docx.oxml.table import CT_Tbl
from docx.oxml.text.paragraph import CT_P
from docx.table import _Cell, Table
from docx.text.paragraph import Paragraph
from typing import Optional, List, Dict
import re
import base64
from io import BytesIO

from app.models.schemas import DocumentType

class DocumentService:
    """文档处理服务类"""
    
    async def convert_to_markdown(self, file_path: str, doc_type: DocumentType) -> str:
        """
        将文档转换为Markdown格式
        
        Args:
            file_path: 文件路径
            doc_type: 文档类型
            
        Returns:
            转换后的Markdown内容
        """
        try:
            if doc_type == DocumentType.MARKDOWN:
                return await self._read_markdown_file(file_path)
            elif doc_type == DocumentType.DOCX:
                return await self._convert_docx_to_markdown(file_path)
            elif doc_type == DocumentType.TXT:
                return await self._convert_txt_to_markdown(file_path)
            else:
                raise ValueError(f"不支持的文档类型: {doc_type}")
                
        except Exception as e:
            raise Exception(f"文档转换失败: {str(e)}")
    
    async def _read_markdown_file(self, file_path: str) -> str:
        """读取Markdown文件"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        return content
    
    async def _convert_txt_to_markdown(self, file_path: str) -> str:
        """将TXT文件转换为Markdown"""
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
        
        # 简单的TXT到Markdown转换
        # 将空行分隔的段落转换为Markdown段落
        paragraphs = content.split('\n\n')
        markdown_content = '\n\n'.join(paragraph.strip() for paragraph in paragraphs if paragraph.strip())
        
        return markdown_content
    
    async def _convert_docx_to_markdown(self, file_path: str) -> str:
        """将DOCX文件转换为Markdown"""
        try:
            doc = Document(file_path)
            markdown_lines = []

            # 创建图片存储目录
            images_dir = os.path.join(os.path.dirname(file_path), "extracted_images")
            os.makedirs(images_dir, exist_ok=True)

            # 处理文档中的所有元素（段落、表格、图片）
            for element in doc.element.body:
                if isinstance(element, CT_P):
                    # 处理段落
                    paragraph = Paragraph(element, doc)
                    paragraph_content = await self._process_paragraph_with_images(paragraph, images_dir)
                    if paragraph_content:
                        markdown_lines.extend(paragraph_content)
                        markdown_lines.append("")  # 添加空行

                elif isinstance(element, CT_Tbl):
                    # 处理表格
                    table = Table(element, doc)
                    markdown_table = self._convert_table_to_markdown(table)
                    if markdown_table:
                        markdown_lines.extend(markdown_table)
                        markdown_lines.append("")

            return '\n'.join(markdown_lines).strip()

        except Exception as e:
            raise Exception(f"DOCX转换失败: {str(e)}")

    async def _process_paragraph_with_images(self, paragraph, images_dir: str) -> List[str]:
        """处理包含图片的段落"""
        result = []

        # 检查段落中是否有图片
        images = self._extract_images_from_paragraph(paragraph, images_dir)

        # 处理文本内容
        text = paragraph.text.strip()
        if text:
            # 检测标题级别（基于样式或格式）
            style_name = paragraph.style.name.lower()

            if 'heading' in style_name:
                # 提取标题级别
                level = self._extract_heading_level(style_name)
                result.append(f"{'#' * level} {text}")
            elif 'title' in style_name:
                result.append(f"# {text}")
            else:
                # 处理普通段落
                formatted_text = self._format_paragraph_text(paragraph)
                result.append(formatted_text)

        # 添加图片
        for image_info in images:
            result.append(f"![{image_info['alt']}]({image_info['path']})")

        return result

    def _extract_images_from_paragraph(self, paragraph, images_dir: str) -> List[Dict]:
        """从段落中提取图片"""
        images = []

        try:
            # 查找段落中的图片
            for run in paragraph.runs:
                for drawing in run.element.xpath('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'}):
                    # 获取图片关系ID
                    rId = drawing.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                    if rId:
                        # 获取图片数据
                        image_part = paragraph.part.related_parts[rId]
                        image_data = image_part.blob

                        # 确定文件扩展名
                        content_type = image_part.content_type
                        if 'jpeg' in content_type or 'jpg' in content_type:
                            ext = '.jpg'
                        elif 'png' in content_type:
                            ext = '.png'
                        elif 'gif' in content_type:
                            ext = '.gif'
                        else:
                            ext = '.jpg'  # 默认

                        # 生成文件名
                        image_filename = f"image_{len(images) + 1}{ext}"
                        image_path = os.path.join(images_dir, image_filename)

                        # 保存图片
                        with open(image_path, 'wb') as f:
                            f.write(image_data)

                        images.append({
                            'alt': f'Image {len(images) + 1}',
                            'path': image_path,
                            'filename': image_filename
                        })

        except Exception as e:
            print(f"提取图片时出错: {e}")

        return images

    def _extract_heading_level(self, style_name: str) -> int:
        """从样式名称中提取标题级别"""
        # 查找数字
        import re
        match = re.search(r'(\d+)', style_name)
        if match:
            level = int(match.group(1))
            return min(level, 6)  # Markdown最多支持6级标题
        return 1
    
    def _format_paragraph_text(self, paragraph) -> str:
        """格式化段落文本，保留基本格式"""
        text = paragraph.text
        
        # 这里可以添加更复杂的格式转换逻辑
        # 例如：粗体、斜体、链接等
        
        return text
    
    def _convert_table_to_markdown(self, table) -> Optional[list]:
        """将Word表格转换为Markdown表格"""
        try:
            if not table.rows:
                return None
            
            markdown_table = []
            
            # 处理表头
            header_row = table.rows[0]
            headers = [cell.text.strip() for cell in header_row.cells]
            markdown_table.append("| " + " | ".join(headers) + " |")
            markdown_table.append("| " + " | ".join(["---"] * len(headers)) + " |")
            
            # 处理数据行
            for row in table.rows[1:]:
                cells = [cell.text.strip() for cell in row.cells]
                # 确保单元格数量与表头一致
                while len(cells) < len(headers):
                    cells.append("")
                markdown_table.append("| " + " | ".join(cells[:len(headers)]) + " |")
            
            return markdown_table
            
        except Exception:
            return None
    
    def validate_markdown(self, content: str) -> dict:
        """验证Markdown内容的有效性"""
        issues = []
        suggestions = []
        
        # 检查标题结构
        lines = content.split('\n')
        heading_levels = []
        
        for i, line in enumerate(lines, 1):
            if line.strip().startswith('#'):
                level = len(line) - len(line.lstrip('#'))
                heading_levels.append((i, level))
        
        # 检查标题级别跳跃
        for i in range(1, len(heading_levels)):
            prev_level = heading_levels[i-1][1]
            curr_level = heading_levels[i][1]
            
            if curr_level > prev_level + 1:
                issues.append(f"第{heading_levels[i][0]}行: 标题级别跳跃过大")
        
        # 检查空标题
        for line_num, level in heading_levels:
            line = lines[line_num - 1]
            if len(line.strip()) <= level + 1:  # 只有#号没有内容
                issues.append(f"第{line_num}行: 空标题")
        
        # 提供优化建议
        if len(heading_levels) == 0:
            suggestions.append("建议添加标题来组织文档结构")
        
        if len([line for line in lines if line.strip()]) < 10:
            suggestions.append("文档内容较少，建议丰富内容")
        
        return {
            "valid": len(issues) == 0,
            "issues": issues,
            "suggestions": suggestions,
            "stats": {
                "lines": len(lines),
                "headings": len(heading_levels),
                "paragraphs": len([line for line in lines if line.strip() and not line.strip().startswith('#')])
            }
        }
