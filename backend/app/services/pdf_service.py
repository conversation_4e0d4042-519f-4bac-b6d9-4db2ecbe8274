"""
简化版PDF生成服务
暂时不使用WeasyPrint，返回模拟数据用于演示
"""

import os
import uuid
import base64
import markdown
from typing import Optional, List, Dict, Any
import asyncio
import time

from app.models.schemas import LayoutConfig
from app.core.config import settings

class PDFService:
    """简化版PDF生成服务类"""
    
    def __init__(self):
        self.output_dir = "generated_pdfs"
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def generate_pdf(
        self, 
        content: str, 
        config: LayoutConfig, 
        filename: Optional[str] = None
    ) -> str:
        """
        生成PDF文件（模拟）
        """
        
        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}.pdf"
        elif not filename.endswith('.pdf'):
            filename += '.pdf'
        
        pdf_path = os.path.join(self.output_dir, filename)
        
        # 模拟PDF生成过程
        await asyncio.sleep(1)  # 模拟处理时间
        
        # 创建一个简单的文本文件作为PDF的占位符
        html_content = self._markdown_to_html(content, config)
        
        # 将HTML内容写入文件（实际应该是PDF）
        with open(pdf_path, 'w', encoding='utf-8') as f:
            f.write(f"PDF文件模拟内容\n生成时间: {time.ctime()}\n\n")
            f.write(f"配置信息:\n")
            f.write(f"页面格式: {config.page_format}\n")
            f.write(f"字体大小: {config.font_size}pt\n")
            f.write(f"行高: {config.line_height}\n\n")
            f.write("原始内容:\n")
            f.write(content)
        
        return pdf_path
    
    def _markdown_to_html(self, content: str, config: LayoutConfig) -> str:
        """将Markdown转换为HTML"""
        
        # 配置Markdown扩展
        extensions = [
            'tables',
            'toc',
            'fenced_code'
        ]
        
        try:
            md = markdown.Markdown(extensions=extensions)
            html_body = md.convert(content)
        except:
            # 如果扩展不可用，使用基础转换
            md = markdown.Markdown()
            html_body = md.convert(content)
        
        # 构建完整HTML文档
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Document</title>
    <style>
        body {{
            font-family: "{config.font_family}", sans-serif;
            font-size: {config.font_size}pt;
            line-height: {config.line_height};
            margin: {config.margin_top}cm {config.margin_right}cm {config.margin_bottom}cm {config.margin_left}cm;
        }}
        p {{
            margin-bottom: {config.paragraph_spacing}pt;
            text-indent: {"2em" if config.indent_first_line else "0"};
        }}
    </style>
</head>
<body>
    <div class="document">
        {html_body}
    </div>
</body>
</html>
"""
        return html_template
    
    async def generate_pdf_preview(self, content: str, config: LayoutConfig) -> str:
        """生成PDF预览（返回base64编码的模拟数据）"""
        
        # 模拟处理时间
        await asyncio.sleep(0.5)
        
        # 返回一个简单的base64编码字符串作为模拟
        preview_text = f"PDF预览模拟数据\n配置: {config.page_format}\n内容长度: {len(content)} 字符"
        return base64.b64encode(preview_text.encode('utf-8')).decode('utf-8')
    
    async def get_page_count(self, pdf_path: str) -> int:
        """获取PDF页数（模拟）"""
        # 简单估算：每500字符一页
        try:
            with open(pdf_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return max(1, len(content) // 500)
        except:
            return 1
    
    def get_pdf_path(self, filename: str) -> str:
        """获取PDF文件路径"""
        return os.path.join(self.output_dir, filename)
    
    def list_generated_pdfs(self) -> List[Dict[str, Any]]:
        """列出已生成的PDF文件"""
        pdfs = []
        
        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                file_path = os.path.join(self.output_dir, filename)
                if os.path.isfile(file_path):
                    file_stats = os.stat(file_path)
                    
                    pdfs.append({
                        "filename": filename,
                        "size": file_stats.st_size,
                        "created_at": file_stats.st_ctime,
                        "download_url": f"/api/pdf/download/{filename}"
                    })
        
        # 按创建时间排序
        pdfs.sort(key=lambda x: x['created_at'], reverse=True)
        return pdfs
    
    def delete_pdf(self, filename: str) -> bool:
        """删除PDF文件"""
        try:
            file_path = os.path.join(self.output_dir, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
