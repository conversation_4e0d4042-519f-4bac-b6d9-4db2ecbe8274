"""
PDF生成服务
使用ReportLab生成真正的PDF文件
"""

import os
import uuid
import base64
import markdown
from typing import Optional, List, Dict, Any
import asyncio
import time
from io import BytesIO

from reportlab.lib.pagesizes import A4, A3, letter, legal
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import cm, inch
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.lib import colors
from reportlab.lib.enums import TA_JUSTIFY, TA_CENTER, TA_LEFT
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import re

from app.models.schemas import LayoutConfig
from app.core.config import settings

class PDFService:
    """PDF生成服务类"""

    def __init__(self):
        self.output_dir = "generated_pdfs"
        os.makedirs(self.output_dir, exist_ok=True)

        # 页面尺寸映射
        self.page_sizes = {
            'A4': A4,
            'A3': A3,
            'Letter': letter,
            'Legal': legal
        }
    
    async def generate_pdf(
        self,
        content: str,
        config: LayoutConfig,
        filename: Optional[str] = None
    ) -> str:
        """
        生成PDF文件
        """

        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}.pdf"
        elif not filename.endswith('.pdf'):
            filename += '.pdf'

        pdf_path = os.path.join(self.output_dir, filename)

        # 在线程池中生成PDF以避免阻塞
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, self._generate_pdf_sync, content, config, pdf_path)

        return pdf_path

    def _generate_pdf_sync(self, content: str, config: LayoutConfig, output_path: str):
        """同步生成PDF"""

        # 获取页面尺寸
        page_size = self.page_sizes.get(config.page_format, A4)

        # 创建PDF文档
        doc = SimpleDocTemplate(
            output_path,
            pagesize=page_size,
            topMargin=config.margin_top * cm,
            bottomMargin=config.margin_bottom * cm,
            leftMargin=config.margin_left * cm,
            rightMargin=config.margin_right * cm
        )

        # 创建样式
        styles = self._create_styles(config)

        # 解析Markdown并转换为PDF元素
        story = self._markdown_to_pdf_elements(content, styles)

        # 构建PDF
        doc.build(story)
    
    def _create_styles(self, config: LayoutConfig) -> Dict[str, ParagraphStyle]:
        """创建PDF样式"""

        base_styles = getSampleStyleSheet()

        # 基础段落样式
        normal_style = ParagraphStyle(
            'Normal',
            parent=base_styles['Normal'],
            fontName='Helvetica',
            fontSize=config.font_size,
            leading=config.font_size * config.line_height,
            spaceAfter=config.paragraph_spacing,
            alignment=TA_JUSTIFY,
            firstLineIndent=20 if config.indent_first_line else 0
        )

        # 标题样式
        heading1_style = ParagraphStyle(
            'Heading1',
            parent=normal_style,
            fontSize=config.font_size * 1.8,
            spaceAfter=config.paragraph_spacing * 2,
            spaceBefore=config.paragraph_spacing * 2,
            alignment=TA_CENTER,
            fontName='Helvetica-Bold'
        )

        heading2_style = ParagraphStyle(
            'Heading2',
            parent=normal_style,
            fontSize=config.font_size * 1.5,
            spaceAfter=config.paragraph_spacing * 1.5,
            spaceBefore=config.paragraph_spacing * 1.5,
            fontName='Helvetica-Bold'
        )

        heading3_style = ParagraphStyle(
            'Heading3',
            parent=normal_style,
            fontSize=config.font_size * 1.3,
            spaceAfter=config.paragraph_spacing,
            spaceBefore=config.paragraph_spacing,
            fontName='Helvetica-Bold'
        )

        # 代码样式
        code_style = ParagraphStyle(
            'Code',
            parent=normal_style,
            fontName='Courier',
            fontSize=config.font_size * 0.9,
            backColor=colors.lightgrey,
            borderColor=colors.grey,
            borderWidth=1,
            borderPadding=5
        )

        return {
            'normal': normal_style,
            'heading1': heading1_style,
            'heading2': heading2_style,
            'heading3': heading3_style,
            'code': code_style
        }
    
    def _markdown_to_pdf_elements(self, content: str, styles: Dict[str, ParagraphStyle]) -> List:
        """将Markdown内容转换为PDF元素"""

        story = []
        lines = content.split('\n')

        i = 0
        while i < len(lines):
            line = lines[i].strip()

            if not line:
                # 空行
                story.append(Spacer(1, 6))
                i += 1
                continue

            # 标题
            if line.startswith('# '):
                text = line[2:].strip()
                story.append(Paragraph(text, styles['heading1']))
            elif line.startswith('## '):
                text = line[3:].strip()
                story.append(Paragraph(text, styles['heading2']))
            elif line.startswith('### '):
                text = line[4:].strip()
                story.append(Paragraph(text, styles['heading3']))

            # 代码块
            elif line.startswith('```'):
                i += 1
                code_lines = []
                while i < len(lines) and not lines[i].strip().startswith('```'):
                    code_lines.append(lines[i])
                    i += 1
                code_text = '\n'.join(code_lines)
                story.append(Paragraph(f'<pre>{code_text}</pre>', styles['code']))

            # 普通段落
            else:
                # 收集连续的非空行作为一个段落
                paragraph_lines = [line]
                i += 1
                while i < len(lines) and lines[i].strip() and not lines[i].strip().startswith('#'):
                    paragraph_lines.append(lines[i].strip())
                    i += 1

                paragraph_text = ' '.join(paragraph_lines)
                # 处理简单的Markdown格式
                paragraph_text = self._process_inline_markdown(paragraph_text)
                story.append(Paragraph(paragraph_text, styles['normal']))
                continue

            i += 1

        return story

    def _process_inline_markdown(self, text: str) -> str:
        """处理行内Markdown格式"""

        # 粗体
        text = re.sub(r'\*\*(.*?)\*\*', r'<b>\1</b>', text)
        text = re.sub(r'__(.*?)__', r'<b>\1</b>', text)

        # 斜体
        text = re.sub(r'\*(.*?)\*', r'<i>\1</i>', text)
        text = re.sub(r'_(.*?)_', r'<i>\1</i>', text)

        # 行内代码
        text = re.sub(r'`(.*?)`', r'<font name="Courier">\1</font>', text)

        return text

    async def generate_pdf_preview(self, content: str, config: LayoutConfig) -> str:
        """生成PDF预览（返回base64编码）"""

        # 生成临时PDF
        temp_filename = f"preview_{uuid.uuid4().hex[:8]}.pdf"
        temp_path = os.path.join(self.output_dir, temp_filename)

        try:
            # 生成PDF
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(None, self._generate_pdf_sync, content, config, temp_path)

            # 读取PDF并转换为base64
            with open(temp_path, 'rb') as f:
                pdf_data = f.read()

            return base64.b64encode(pdf_data).decode('utf-8')

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)
    
    async def get_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            from PyPDF2 import PdfReader
            with open(pdf_path, 'rb') as f:
                pdf_reader = PdfReader(f)
                return len(pdf_reader.pages)
        except Exception:
            # 如果PyPDF2不可用，简单估算
            try:
                file_size = os.path.getsize(pdf_path)
                # 估算：每页约5KB
                return max(1, file_size // 5000)
            except:
                return 1
    
    def get_pdf_path(self, filename: str) -> str:
        """获取PDF文件路径"""
        return os.path.join(self.output_dir, filename)
    
    def list_generated_pdfs(self) -> List[Dict[str, Any]]:
        """列出已生成的PDF文件"""
        pdfs = []

        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.pdf'):
                    file_path = os.path.join(self.output_dir, filename)
                    if os.path.isfile(file_path):
                        file_stats = os.stat(file_path)

                        pdfs.append({
                            "filename": filename,
                            "size": file_stats.st_size,
                            "created_at": file_stats.st_ctime,
                            "download_url": f"/api/pdf/download/{filename}"
                        })

        # 按创建时间排序
        pdfs.sort(key=lambda x: x['created_at'], reverse=True)
        return pdfs
    
    def delete_pdf(self, filename: str) -> bool:
        """删除PDF文件"""
        try:
            file_path = os.path.join(self.output_dir, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False
