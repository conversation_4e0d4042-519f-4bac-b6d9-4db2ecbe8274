"""
PDF生成服务
使用WeasyPrint生成高质量PDF
"""

import os
import uuid
import base64
import markdown
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration
from typing import Optional, List, Dict, Any
import asyncio
from concurrent.futures import ThreadPoolExecutor
import PyPDF2

from app.models.schemas import LayoutConfig
from app.core.config import settings

class PDFService:
    """PDF生成服务类"""
    
    def __init__(self):
        self.output_dir = "generated_pdfs"
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 配置字体
        self.font_config = FontConfiguration()
        
        # 线程池用于异步PDF生成
        self.executor = ThreadPoolExecutor(max_workers=2)
    
    async def generate_pdf(
        self, 
        content: str, 
        config: LayoutConfig, 
        filename: Optional[str] = None
    ) -> str:
        """
        生成PDF文件
        
        Args:
            content: Markdown内容
            config: 排版配置
            filename: 输出文件名
            
        Returns:
            生成的PDF文件路径
        """
        
        # 生成文件名
        if not filename:
            filename = f"document_{uuid.uuid4().hex[:8]}.pdf"
        elif not filename.endswith('.pdf'):
            filename += '.pdf'
        
        pdf_path = os.path.join(self.output_dir, filename)
        
        # 在线程池中执行PDF生成
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(
            self.executor,
            self._generate_pdf_sync,
            content,
            config,
            pdf_path
        )
        
        return pdf_path
    
    def _generate_pdf_sync(self, content: str, config: LayoutConfig, output_path: str):
        """同步生成PDF"""
        
        # 转换Markdown为HTML
        html_content = self._markdown_to_html(content, config)
        
        # 生成CSS样式
        css_content = self._generate_pdf_css(config)
        
        # 使用WeasyPrint生成PDF
        html_doc = HTML(string=html_content)
        css_doc = CSS(string=css_content, font_config=self.font_config)
        
        html_doc.write_pdf(
            output_path,
            stylesheets=[css_doc],
            font_config=self.font_config
        )
    
    def _markdown_to_html(self, content: str, config: LayoutConfig) -> str:
        """将Markdown转换为HTML"""
        
        # 配置Markdown扩展
        extensions = [
            'tables',
            'toc',
            'codehilite',
            'fenced_code',
            'attr_list'
        ]
        
        md = markdown.Markdown(extensions=extensions)
        html_body = md.convert(content)
        
        # 构建完整HTML文档
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Document</title>
</head>
<body>
    <div class="document">
        {html_body}
    </div>
</body>
</html>
"""
        return html_template
    
    def _generate_pdf_css(self, config: LayoutConfig) -> str:
        """生成PDF专用CSS样式"""
        
        # 页面设置
        page_css = f"""
        @page {{
            size: {config.page_format};
            margin: {config.margin_top}cm {config.margin_right}cm {config.margin_bottom}cm {config.margin_left}cm;
            
            @top-center {{
                content: counter(page);
                font-size: 10pt;
                color: #666;
            }}
        }}
        """
        
        # 基础样式
        base_css = f"""
        body {{
            font-family: "{config.font_family}", "Noto Sans CJK SC", "SimSun", serif;
            font-size: {config.font_size}pt;
            line-height: {config.line_height};
            color: #000;
            margin: 0;
            padding: 0;
        }}
        
        .document {{
            width: 100%;
        }}
        
        /* 段落样式 */
        p {{
            margin: 0 0 {config.paragraph_spacing}pt 0;
            text-indent: {"2em" if config.indent_first_line else "0"};
            text-align: justify;
            orphans: 2;
            widows: 2;
        }}
        
        /* 标题样式 */
        h1, h2, h3, h4, h5, h6 {{
            margin: {config.paragraph_spacing * 2}pt 0 {config.paragraph_spacing}pt 0;
            text-indent: 0;
            page-break-after: avoid;
            orphans: 3;
            widows: 3;
        }}
        
        h1 {{
            font-size: {config.font_size * 2}pt;
            font-weight: bold;
            text-align: center;
            page-break-before: always;
        }}
        
        h2 {{
            font-size: {config.font_size * 1.5}pt;
            font-weight: bold;
            border-bottom: 2pt solid #333;
            padding-bottom: 4pt;
        }}
        
        h3 {{
            font-size: {config.font_size * 1.3}pt;
            font-weight: bold;
        }}
        
        h4 {{
            font-size: {config.font_size * 1.1}pt;
            font-weight: bold;
        }}
        
        h5, h6 {{
            font-size: {config.font_size}pt;
            font-weight: bold;
        }}
        """
        
        # 表格样式
        table_css = """
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 12pt 0;
            page-break-inside: avoid;
        }
        
        th, td {
            border: 1pt solid #333;
            padding: 6pt;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f0f0f0;
            font-weight: bold;
            text-align: center;
        }
        
        tr {
            page-break-inside: avoid;
        }
        """
        
        # 代码样式
        code_css = f"""
        code {{
            font-family: "Courier New", "Monaco", monospace;
            font-size: {config.font_size * 0.9}pt;
            background-color: #f5f5f5;
            padding: 2pt 4pt;
            border-radius: 2pt;
        }}
        
        pre {{
            font-family: "Courier New", "Monaco", monospace;
            font-size: {config.font_size * 0.85}pt;
            background-color: #f5f5f5;
            padding: 8pt;
            border-radius: 4pt;
            border: 1pt solid #ddd;
            overflow-x: auto;
            page-break-inside: avoid;
            white-space: pre-wrap;
        }}
        
        pre code {{
            background: none;
            padding: 0;
        }}
        """
        
        # 列表样式
        list_css = f"""
        ul, ol {{
            margin: {config.paragraph_spacing}pt 0;
            padding-left: 2em;
        }}
        
        li {{
            margin-bottom: {config.paragraph_spacing / 2}pt;
            text-indent: 0;
        }}
        """
        
        # 其他元素样式
        misc_css = f"""
        blockquote {{
            margin: {config.paragraph_spacing}pt 2em;
            padding: 8pt 16pt;
            border-left: 4pt solid #ddd;
            background-color: #f9f9f9;
            font-style: italic;
        }}
        
        hr {{
            border: none;
            border-top: 1pt solid #333;
            margin: {config.paragraph_spacing * 2}pt 0;
        }}
        
        img {{
            max-width: 100%;
            height: auto;
            display: block;
            margin: {config.paragraph_spacing}pt auto;
        }}
        
        /* 分页控制 */
        .page-break {{
            page-break-before: always;
        }}
        
        .no-break {{
            page-break-inside: avoid;
        }}
        """
        
        # 印刷特定样式
        print_css = ""
        if config.color_mode.value == "CMYK":
            print_css = """
            * {
                color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
            """
        
        return page_css + base_css + table_css + code_css + list_css + misc_css + print_css

    async def generate_pdf_preview(self, content: str, config: LayoutConfig) -> str:
        """生成PDF预览（返回base64编码）"""

        # 生成临时PDF
        temp_filename = f"preview_{uuid.uuid4().hex[:8]}.pdf"
        temp_path = os.path.join(self.output_dir, temp_filename)

        try:
            # 生成PDF
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                self.executor,
                self._generate_pdf_sync,
                content,
                config,
                temp_path
            )

            # 读取PDF并转换为base64
            with open(temp_path, 'rb') as f:
                pdf_data = f.read()

            base64_data = base64.b64encode(pdf_data).decode('utf-8')

            return base64_data

        finally:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.remove(temp_path)

    async def get_page_count(self, pdf_path: str) -> int:
        """获取PDF页数"""
        try:
            with open(pdf_path, 'rb') as f:
                pdf_reader = PyPDF2.PdfReader(f)
                return len(pdf_reader.pages)
        except Exception:
            return 1  # 默认返回1页

    def get_pdf_path(self, filename: str) -> str:
        """获取PDF文件路径"""
        return os.path.join(self.output_dir, filename)

    def list_generated_pdfs(self) -> List[Dict[str, Any]]:
        """列出已生成的PDF文件"""
        pdfs = []

        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.pdf'):
                    file_path = os.path.join(self.output_dir, filename)
                    file_stats = os.stat(file_path)

                    pdfs.append({
                        "filename": filename,
                        "size": file_stats.st_size,
                        "created_at": file_stats.st_ctime,
                        "download_url": f"/api/pdf/download/{filename}"
                    })

        # 按创建时间排序
        pdfs.sort(key=lambda x: x['created_at'], reverse=True)
        return pdfs

    def delete_pdf(self, filename: str) -> bool:
        """删除PDF文件"""
        try:
            file_path = os.path.join(self.output_dir, filename)
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False

    def cleanup_old_pdfs(self, max_age_hours: int = 24):
        """清理旧的PDF文件"""
        import time

        current_time = time.time()
        max_age_seconds = max_age_hours * 3600

        if os.path.exists(self.output_dir):
            for filename in os.listdir(self.output_dir):
                if filename.endswith('.pdf'):
                    file_path = os.path.join(self.output_dir, filename)
                    file_age = current_time - os.path.getctime(file_path)

                    if file_age > max_age_seconds:
                        try:
                            os.remove(file_path)
                        except Exception:
                            pass  # 忽略删除失败的文件
