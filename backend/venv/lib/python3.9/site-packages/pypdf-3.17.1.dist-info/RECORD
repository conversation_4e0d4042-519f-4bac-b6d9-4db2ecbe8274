../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_cmap.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/adobe_glyphs.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/pdfdoc.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/std.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/symbol.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_codecs/zapfding.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_crypt_providers/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_crypt_providers/_base.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_crypt_providers/_cryptography.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_crypt_providers/_fallback.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_crypt_providers/_pycryptodome.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_encryption.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_merger.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_page.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_page_labels.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_protocols.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_reader.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_text_extraction/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_utils.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_version.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_writer.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/_xobj_image_helpers.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/annotations/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/annotations/_base.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/annotations/_markup_annotations.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/annotations/_non_markup_annotations.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/constants.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/errors.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/filters.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/__init__.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_base.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_data_structures.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_fit.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_outline.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_rectangle.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_utils.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/generic/_viewerpref.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/pagerange.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/papersizes.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/types.cpython-39.pyc,,
../../../../../../../../Library/Caches/com.apple.python/Users/<USER>/Documents/augment-projects/PrintMind/backend/venv/lib/python3.9/site-packages/pypdf/xmp.cpython-39.pyc,,
pypdf-3.17.1.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pypdf-3.17.1.dist-info/LICENSE,sha256=qXrCMOXzPvEKU2eoUOsB-R8aCwZONHQsd5TSKUVX9SQ,1605
pypdf-3.17.1.dist-info/METADATA,sha256=LkmpkRveoVdBu6s4x_ZU8BsS_gQq0y3HWKomSPt7Iv0,7458
pypdf-3.17.1.dist-info/RECORD,,
pypdf-3.17.1.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf-3.17.1.dist-info/WHEEL,sha256=EZbGkh7Ie4PoZfRQ8I0ZuP9VklN_TvcZ6DSE5Uar4z4,81
pypdf/__init__.py,sha256=YQmop7YmjbxLJykhRWAR6bWhM2dC6Tmfb4Sx2lkzTdY,1574
pypdf/_cmap.py,sha256=6Yjmf1c_F3Na3G22RPH2aB3zefIrrOlwtwywz3u1Ot0,17978
pypdf/_codecs/__init__.py,sha256=15Fls0Fzl2NXKJyGNO4ozWveYCbOtDkdFiUSUpxHVGQ,1674
pypdf/_codecs/adobe_glyphs.py,sha256=jrMZTzGFE8aMEuwfNJ4JZh_GZypPBg6SLE1oaC9DRTU,447237
pypdf/_codecs/pdfdoc.py,sha256=xfSvMFYsvxuaSQ0Uu9vZDKaB0Wu85h1uCiB1i9rAcUU,4269
pypdf/_codecs/std.py,sha256=DyQMuEpAGEpS9uy1jWf4cnj-kqShPOAij5sI7Q1YD8E,2630
pypdf/_codecs/symbol.py,sha256=nIaGQIlhWCJiPMHrwUlmGHH-_fOXyEKvguRmuKXcGAk,3734
pypdf/_codecs/zapfding.py,sha256=PQxjxRC616d41xF3exVxP1W8nM4QrZfjO3lmtLxpE_s,3742
pypdf/_crypt_providers/__init__.py,sha256=O6cOQ1QYca10IV_YDo1RE6PzCs-rxL9pNGmP__nRGkE,3054
pypdf/_crypt_providers/_base.py,sha256=_f53Mj6vivhEZMQ4vNxN5G0IOgFY-n5_leke0c_qiNU,1711
pypdf/_crypt_providers/_cryptography.py,sha256=HTw4Ti43ed8kFSysly_YTH-lDRQlBI4PU9DPNLvPgu8,4329
pypdf/_crypt_providers/_fallback.py,sha256=PVDQQrq389VbaBqOHxXfoyCk9bLYgFrrDKVpNXzTdx8,3345
pypdf/_crypt_providers/_pycryptodome.py,sha256=U1aQZ9iYBrZo-hKCjJUhGOPhwEFToiitowQ316TNrrA,3381
pypdf/_encryption.py,sha256=dYr5XWS7eeio98O_-V2lDm1K-KYrBUiKExgUXXchNQ4,48918
pypdf/_merger.py,sha256=jp3FD5yM_79-W3QFUdvtPcYi_9hWVBJlTO7TXD-x320,30402
pypdf/_page.py,sha256=Qc4pcRptuvNYAu7hUytUUyrAvkRtAg_jb_Gy3F_sZ78,101724
pypdf/_page_labels.py,sha256=XzUWAwjCl72TQqn1TI_OnhoH42OVT4qTVnIYJt2nIek,7186
pypdf/_protocols.py,sha256=BQEmqrBY4zapSDfjmo4zWlcCe4fQMWyoAXm2NAATbjQ,1892
pypdf/_reader.py,sha256=5Qc_AU-lh-daJga0JVYZD9fgl6eXjdDZb4QdOhCKz6g,87906
pypdf/_text_extraction/__init__.py,sha256=065T2vEZWRxTTnhQuAsx4UKZaUyKAP9vO8_zA8RAF8Y,10309
pypdf/_utils.py,sha256=upb6HTYf-jH20F47rMIV63pPpp46PmPdXWUIeXqVTvM,20001
pypdf/_version.py,sha256=cJ8uf9AjtVGPTM08krf9RgTaf15cSGI6lMxDzKaEIhM,23
pypdf/_writer.py,sha256=WiCJKZp1LJFgwTUSSJXyGfcZNHyTHIH3SAzazVWYQWU,137258
pypdf/_xobj_image_helpers.py,sha256=pnPCX1z3bet3uweS7c35QtC1o9KDn_AKqtRkJV16A2o,9978
pypdf/annotations/__init__.py,sha256=5Ojv2DCGTfXwhxbSBcrcaU9Y2Q1O5QmBRMcWgyxPcxQ,1118
pypdf/annotations/_base.py,sha256=axdUsbNrhD1bHCJoWSKCspgNGe61aK2Vzn9jlaEq3zc,910
pypdf/annotations/_markup_annotations.py,sha256=48K7_4gcNdkd3MV5dKR3Ygx5BQsyHgzbJm01wn0mJWM,11597
pypdf/annotations/_non_markup_annotations.py,sha256=a8wCa_0-ZAhOWfnLMrkmVnGBTBy6QkfLyeUIz5G0TQ0,1278
pypdf/constants.py,sha256=Y71t16r7LE90uIfbw2x6zJteSA-HPwwDtwEJMtHoNgw,17332
pypdf/errors.py,sha256=G2_fqPaJneNR4Q5o8ELem77qW00xdx9dtPc5RWwGGJI,1632
pypdf/filters.py,sha256=rfOD5waqm7ddlhuDCgyQtJRUN2CggM43SjbIzIzOYfE,30284
pypdf/generic/__init__.py,sha256=1jsEBbojhzzylIup74TdHRuVO-pHfjWpc4KoY6HGTkU,15123
pypdf/generic/_base.py,sha256=YgdcqmNk9SKcfakMZjpEoc_rrbJSUDNAJ0Yh-v9oVM0,26641
pypdf/generic/_data_structures.py,sha256=tTG0zyaQX3KqesRno8UrHN9abnlRZJisxHGlmff0D-s,60895
pypdf/generic/_fit.py,sha256=_2bbohJwlpEltNaLp8j5t8dSNs3270av_d8A5FvM32o,5427
pypdf/generic/_outline.py,sha256=NECObZjyDb3g3g7yN7E6h_C1CsFwAYoUNc_XMcYbgSU,1344
pypdf/generic/_rectangle.py,sha256=pHg3y3M1c_13baDBz_x5cSVjnJRjd4wHU9Pc5cA0Nyg,8840
pypdf/generic/_utils.py,sha256=KogUXJL3bdR9x8CWUQVvAJa1nj1nvmJdB1_pUbXdKyc,6420
pypdf/generic/_viewerpref.py,sha256=urolnUJqs0Arb6YSbFcjOqU0Y55EuwJ18_QuWoZ1GPA,6327
pypdf/pagerange.py,sha256=6S6QOKzvA2lcFrzksSh8J4hs4uwgIl_53s4ilPcPq5o,6880
pypdf/papersizes.py,sha256=ACzkcH77rrMfn5lp66mZPoYaFJiPjdwEetIQlOBXGMw,1367
pypdf/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pypdf/types.py,sha256=uWuyr75GBV48RRaWCEz90P3-oogcqA5qV1kB7_n8QnA,2113
pypdf/xmp.py,sha256=p0HOJrpjrMz-0jqJUIDADQyJmUOHPgGsofKtsNoX4Eg,18064
