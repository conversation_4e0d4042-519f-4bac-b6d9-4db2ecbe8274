MacRomanEncoding = (
                 None, None, None, None, None, None, None, None, None, None, None, None,
                 None, None, None, None, None, None, None, None, None, None, None, None,
                 None, None, None, None, None, None, None, None, 'space', 'exclam',
                 'quotedbl', 'numbersign', 'dollar', 'percent', 'ampersand',
                 'quotesingle', 'parenleft', 'parenright', 'asterisk', 'plus', 'comma',
                 'hyphen', 'period', 'slash', 'zero', 'one', 'two', 'three', 'four',
                 'five', 'six', 'seven', 'eight', 'nine', 'colon', 'semicolon', 'less',
                 'equal', 'greater', 'question', 'at', 'A', 'B', 'C', 'D', 'E', 'F',
                 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T',
                 'U', 'V', 'W', 'X', 'Y', 'Z', 'bracketleft', 'backslash', 'bracketright',
                 'asciicircum', 'underscore', 'grave', 'a', 'b', 'c', 'd', 'e', 'f',
                 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't',
                 'u', 'v', 'w', 'x', 'y', 'z', 'braceleft', 'bar', 'braceright',
                 'asciitilde', None, 'Adieresis', 'Aring', 'Ccedilla', 'Eacute',
                 'Ntilde', 'Odieresis', 'Udieresis', 'aacute', 'agrave', 'acircumflex',
                 'adieresis', 'atilde', 'aring', 'ccedilla', 'eacute', 'egrave',
                 'ecircumflex', 'edieresis', 'iacute', 'igrave', 'icircumflex',
                 'idieresis', 'ntilde', 'oacute', 'ograve', 'ocircumflex', 'odieresis',
                 'otilde', 'uacute', 'ugrave', 'ucircumflex', 'udieresis', 'dagger',
                 'degree', 'cent', 'sterling', 'section', 'bullet', 'paragraph',
                 'germandbls', 'registered', 'copyright', 'trademark', 'acute',
                 'dieresis', None, 'AE', 'Oslash', None, 'plusminus', None, None, 'yen',
                 'mu', None, None, None, None, None, 'ordfeminine', 'ordmasculine', None,
                 'ae', 'oslash', 'questiondown', 'exclamdown', 'logicalnot', None, 'florin',
                 None, None, 'guillemotleft', 'guillemotright', 'ellipsis', 'space', 'Agrave',
                 'Atilde', 'Otilde', 'OE', 'oe', 'endash', 'emdash', 'quotedblleft',
                 'quotedblright', 'quoteleft', 'quoteright', 'divide', None, 'ydieresis',
                 'Ydieresis', 'fraction', 'currency', 'guilsinglleft', 'guilsinglright',
                 'fi', 'fl', 'daggerdbl', 'periodcentered', 'quotesinglbase',
                 'quotedblbase', 'perthousand', 'Acircumflex', 'Ecircumflex', 'Aacute',
                 'Edieresis', 'Egrave', 'Iacute', 'Icircumflex', 'Idieresis', 'Igrave',
                 'Oacute', 'Ocircumflex', None, 'Ograve', 'Uacute', 'Ucircumflex',
                 'Ugrave', 'dotlessi', 'circumflex', 'tilde', 'macron', 'breve',
                 'dotaccent', 'ring', 'cedilla', 'hungarumlaut', 'ogonek', 'caron')
