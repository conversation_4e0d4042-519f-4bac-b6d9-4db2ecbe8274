<template>
  <div class="pdf-preview h-full flex flex-col">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50">
      <div class="flex items-center space-x-2">
        <button
          @click="refreshPreview"
          :disabled="isLoading"
          class="px-3 py-1 text-sm bg-primary-100 text-primary-700 rounded hover:bg-primary-200 disabled:opacity-50"
        >
          {{ isLoading ? '生成中...' : '刷新预览' }}
        </button>
        
        <select v-model="previewMode" class="text-sm border border-gray-300 rounded px-2 py-1">
          <option value="html">HTML预览</option>
          <option value="pdf">PDF预览</option>
        </select>
      </div>

      <div class="flex items-center space-x-2">
        <button
          v-if="previewMode === 'html'"
          @click="zoomOut"
          :disabled="zoomLevel <= 50"
          class="p-1 text-gray-600 hover:text-gray-900 disabled:opacity-50"
        >
          🔍-
        </button>
        
        <span v-if="previewMode === 'html'" class="text-sm text-gray-600">
          {{ zoomLevel }}%
        </span>
        
        <button
          v-if="previewMode === 'html'"
          @click="zoomIn"
          :disabled="zoomLevel >= 200"
          class="p-1 text-gray-600 hover:text-gray-900 disabled:opacity-50"
        >
          🔍+
        </button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content flex-1 overflow-auto bg-gray-100">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"></div>
          <p class="text-gray-600">正在生成预览...</p>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="flex items-center justify-center h-full">
        <div class="text-center">
          <div class="text-red-500 text-4xl mb-4">⚠️</div>
          <p class="text-red-600 mb-4">{{ error }}</p>
          <button @click="refreshPreview" class="btn-primary">
            重试
          </button>
        </div>
      </div>

      <!-- HTML预览 -->
      <div
        v-else-if="previewMode === 'html' && htmlPreview"
        class="html-preview-container p-4"
      >
        <div
          class="html-preview bg-white shadow-lg mx-auto"
          :style="{ 
            transform: `scale(${zoomLevel / 100})`,
            transformOrigin: 'top center',
            width: getPageWidth(),
            minHeight: getPageHeight()
          }"
        >
          <div
            class="preview-page p-8"
            v-html="htmlPreview"
          ></div>
        </div>
      </div>

      <!-- PDF预览 -->
      <div
        v-else-if="previewMode === 'pdf' && pdfPreview"
        class="pdf-preview-container p-4"
      >
        <iframe
          :src="pdfPreview"
          class="w-full h-full border-0 bg-white shadow-lg mx-auto"
          style="min-height: 600px;"
        ></iframe>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center h-full">
        <div class="text-center text-gray-500">
          <div class="text-4xl mb-4">📄</div>
          <p>输入内容后将显示预览</p>
        </div>
      </div>
    </div>

    <!-- 预览信息 -->
    <div v-if="previewInfo" class="preview-info p-3 border-t border-gray-200 bg-gray-50">
      <div class="flex items-center justify-between text-xs text-gray-600">
        <div class="flex items-center space-x-4">
          <span>页面: {{ previewInfo.pageFormat }}</span>
          <span>字体: {{ previewInfo.fontSize }}pt</span>
          <span>行高: {{ previewInfo.lineHeight }}</span>
        </div>
        <div class="flex items-center space-x-4">
          <span>预计页数: {{ estimatedPages }}</span>
          <span>字数: {{ wordCount }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { layoutAPI, pdfAPI } from '@/utils/api'
import type { LayoutConfig } from '@/types/layout'

// 组件属性
interface Props {
  content: string
  config: LayoutConfig
}

const props = defineProps<Props>()

// 响应式数据
const isLoading = ref(false)
const error = ref('')
const previewMode = ref<'html' | 'pdf'>('html')
const htmlPreview = ref('')
const pdfPreview = ref('')
const zoomLevel = ref(100)

// 计算属性
const previewInfo = computed(() => {
  if (!props.config) return null
  
  return {
    pageFormat: props.config.page_format,
    fontSize: props.config.font_size,
    lineHeight: props.config.line_height
  }
})

const wordCount = computed(() => {
  if (!props.content) return 0
  
  // 简单的中英文字数统计
  const chinese = (props.content.match(/[\u4e00-\u9fff]/g) || []).length
  const english = (props.content.match(/[a-zA-Z]+/g) || []).join('').length
  return chinese + Math.ceil(english / 4)
})

const estimatedPages = computed(() => {
  if (!props.content || !props.config) return 1
  
  // 简单的页数估算
  const wordsPerPage = 500 // 假设每页500字
  return Math.max(1, Math.ceil(wordCount.value / wordsPerPage))
})

// 方法
const getPageWidth = () => {
  const format = props.config?.page_format || 'A4'
  const widths = {
    'A4': '21cm',
    'A3': '29.7cm',
    'Letter': '8.5in',
    'Legal': '8.5in'
  }
  return widths[format] || '21cm'
}

const getPageHeight = () => {
  const format = props.config?.page_format || 'A4'
  const heights = {
    'A4': '29.7cm',
    'A3': '42cm',
    'Letter': '11in',
    'Legal': '14in'
  }
  return heights[format] || '29.7cm'
}

const zoomIn = () => {
  if (zoomLevel.value < 200) {
    zoomLevel.value += 25
  }
}

const zoomOut = () => {
  if (zoomLevel.value > 50) {
    zoomLevel.value -= 25
  }
}

const refreshPreview = async () => {
  // 检查是否有内容
  if (!props.content || !props.content.trim()) {
    htmlPreview.value = ''
    pdfPreview.value = ''
    error.value = ''
    return
  }

  isLoading.value = true
  error.value = ''

  try {
    if (previewMode.value === 'html') {
      await generateHTMLPreview()
    } else {
      await generatePDFPreview()
    }
  } catch (err: any) {
    console.error('预览生成失败:', err)
    error.value = err.response?.data?.detail || err.message || '预览生成失败'
  } finally {
    isLoading.value = false
  }
}

const generateHTMLPreview = async () => {
  try {
    const response = await layoutAPI.preview(props.content, props.config)
    if (response.success) {
      htmlPreview.value = response.html
    } else {
      throw new Error(response.message || 'HTML预览生成失败')
    }
  } catch (error) {
    throw error
  }
}

const generatePDFPreview = async () => {
  try {
    const response = await pdfAPI.preview({
      content: props.content,
      layout_config: props.config
    })
    
    if (response.success && response.pdf_data) {
      // 创建PDF blob URL
      const pdfBlob = new Blob(
        [Uint8Array.from(atob(response.pdf_data), c => c.charCodeAt(0))],
        { type: 'application/pdf' }
      )
      pdfPreview.value = URL.createObjectURL(pdfBlob)
    } else {
      throw new Error('PDF预览生成失败')
    }
  } catch (error) {
    throw error
  }
}

// 监听内容和配置变化
watch([() => props.content, () => props.config], () => {
  // 防抖处理
  clearTimeout(refreshPreview.timeout)
  refreshPreview.timeout = setTimeout(() => {
    if (props.content && props.content.trim()) {
      refreshPreview()
    } else {
      // 清空预览
      htmlPreview.value = ''
      pdfPreview.value = ''
      error.value = ''
    }
  }, 1000)
}, { deep: true })

// 监听预览模式变化
watch(previewMode, () => {
  if (props.content && props.content.trim()) {
    refreshPreview()
  }
})

// 组件挂载时不自动生成预览，等待内容输入

// 清理资源
const cleanup = () => {
  if (pdfPreview.value && pdfPreview.value.startsWith('blob:')) {
    URL.revokeObjectURL(pdfPreview.value)
  }
}

// 组件卸载时清理
onMounted(() => {
  return cleanup
})
</script>

<style scoped>
.pdf-preview {
  @apply border border-gray-300 rounded-lg overflow-hidden;
}

.html-preview {
  @apply transition-transform duration-200;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.preview-page {
  font-family: inherit;
  line-height: inherit;
}

.html-preview-container {
  @apply flex justify-center;
  min-height: 100%;
}

.pdf-preview-container {
  @apply flex justify-center;
  min-height: 100%;
}

/* 预览内容样式 */
.preview-page :deep(h1) {
  @apply text-2xl font-bold mb-4 mt-6;
}

.preview-page :deep(h2) {
  @apply text-xl font-bold mb-3 mt-5;
}

.preview-page :deep(h3) {
  @apply text-lg font-bold mb-2 mt-4;
}

.preview-page :deep(p) {
  @apply mb-4 leading-relaxed;
}

.preview-page :deep(ul), 
.preview-page :deep(ol) {
  @apply mb-4 pl-6;
}

.preview-page :deep(li) {
  @apply mb-1;
}

.preview-page :deep(table) {
  @apply w-full border-collapse mb-4;
}

.preview-page :deep(th), 
.preview-page :deep(td) {
  @apply border border-gray-300 px-3 py-2 text-left;
}

.preview-page :deep(th) {
  @apply bg-gray-50 font-semibold;
}

.preview-page :deep(code) {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.preview-page :deep(pre) {
  @apply bg-gray-100 p-4 rounded overflow-x-auto mb-4;
}

.preview-page :deep(blockquote) {
  @apply border-l-4 border-gray-300 pl-4 italic mb-4;
}
</style>
